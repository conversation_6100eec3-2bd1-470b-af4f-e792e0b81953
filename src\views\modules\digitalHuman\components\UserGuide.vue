<template>
	<!-- 
    数字人编辑页面用户引导组件
    功能概览：
    - 分步骤引导用户了解数字人编辑功能
    - 通过10张引导图片逐步指导操作
    - 全屏覆盖层显示，支持透明点击区域
    - 首次访问时自动显示，完成后不再显示
  -->
	<div v-if="isVisible" class="user-guide-overlay" @keydown="handleKeydown" tabindex="0">
		<!-- 全屏背景遮罩 -->
		<div class="guide-backdrop" @click="handleBackdropClick"></div>

		<!-- 引导内容容器 -->
		<div class="guide-content-container">
			<!-- 当前引导图片 -->
			<div class="guide-image-wrapper">
				<img :src="currentGuideStep.image" :alt="`引导步骤 ${currentStep}`" class="guide-image"
					@load="handleImageLoad" @error="handleImageError" />

				<!-- 透明点击区域 - 支持多个按钮 -->
				<template v-if="currentGuideStep.clickAreas">
					<!-- 多个点击区域 -->
					<div v-for="(area, index) in currentGuideStep.clickAreas" :key="index"
						class="click-area" :style="area"
						@click="handleAreaClick(area.action)"
						:title="area.action === 'prev' ? '上一步' : (area.action === 'next' ? (currentStep < totalSteps ? '下一步' : '完成') : (area.action === 'close' ? '关闭引导' : '点击继续'))">
					</div>
				</template>
			</div>



			<!-- 控制按钮 - 隐藏，只保留进度条和透明点击区域 -->
			<!--
			<div class="guide-controls">
				<button class="skip-button" @click="skipGuide" :disabled="isTransitioning">
					跳过引导
				</button>

				<button v-if="currentStep > 1" class="prev-button" @click="prevStep" :disabled="isTransitioning">
					上一步
				</button>

				<button class="next-button" @click="nextStep" :disabled="isTransitioning">
					{{ currentStep < totalSteps ? '下一步' : '完成引导' }} </button>
			</div>
			-->
		</div>

		<!-- 加载状态 -->
		<div v-if="isLoading" class="loading-overlay">
			<div class="loading-spinner"></div>
			<p class="loading-text">正在加载引导内容...</p>
		</div>
	</div>
</template>

<script setup>
// ========================================
// 📦 核心依赖导入
// ========================================
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';

// ========================================
// 🎯 引导步骤数据配置
// ========================================
/**
 * 引导步骤配置数组
 *
 * 🎯 功能：定义每个引导步骤的图片URL和点击区域位置
 * 📊 数据结构：
 * - image: 引导图片的URL地址
 * - clickAreas: 透明点击区域数组，每个区域包含：
 *   - position: CSS定位方式（通常为'absolute'）
 *   - top/bottom: 距离顶部/底部的百分比位置
 *   - left/right: 距离左侧/右侧的百分比位置
 *   - width: 点击区域的宽度
 *   - height: 点击区域的高度
 *   - action: 点击动作类型（'prev'|'next'|'close'）
 *
 * 🛠️ 注意事项：
 * - 支持多个点击区域：上一步、下一步、关闭按钮
 * - 使用百分比定位确保在不同屏幕尺寸下的适配性
 * - 点击区域应该稍大于按钮实际大小，提升用户体验
 */
const guideSteps = ref([
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao1.png',
		clickAreas: [
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '18%',
				right: '53%',
				width: '80px',
				height: '45px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '67%',
				right: '52.7%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao2.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '73%',
				right: '68.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '73%',
				right: '64.3%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '10.5%',
				right: '64%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao3.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '63.3%',
				right: '68.9%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '63.3%',
				right: '64.8%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '20.3%',
				right: '64.5%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao4.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '78%',
				right: '22.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '78%',
				right: '18.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '5.5%',
				right: '18%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao5.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '26.3%',
				right: '22.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '26.3%',
				right: '18.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '57.5%',
				right: '18.1%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao6.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '58.5%',
				right: '8.1%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '58.5%',
				right: '4%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '25%',
				right: '3.8%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao7.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '1.5%',
				right: '22.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '1.5%',
				right: '18.5%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '84%',
				right: '18.1%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao8.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '72%',
				right: '68%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '72%',
				right: '64%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '11.7%',
				right: '63.8%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao9.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '13.7%',
				right: '31.1%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 下一步按钮
				position: 'absolute',
				bottom: '13.7%',
				right: '27%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '70%',
				right: '26.7%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	},
	{
		image: 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/yindao10.png',
		clickAreas: [
			{
				// 上一步按钮
				position: 'absolute',
				bottom: '75.5%',
				right: '17%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'prev'
			},
			{
				// 完成按钮（第十步是最后一步）
				position: 'absolute',
				bottom: '75.5%',
				right: '13%',
				width: '80px',
				height: '40px',
				cursor: 'pointer',
				action: 'next'
			},
			{
				// X号关闭按钮
				position: 'absolute',
				top: '10%',
				right: '12.7%',
				width: '30px',
				height: '30px',
				cursor: 'pointer',
				action: 'close'
			}
		]
	}
]);

// ========================================
// 📊 响应式状态管理
// ========================================
// 引导是否可见 - 默认隐藏，只有需要时才显示
const isVisible = ref(false);
// 当前引导步骤（1-10）
const currentStep = ref(1);
// 是否正在过渡中（防止快速点击）
const isTransitioning = ref(false);
// 是否正在加载
const isLoading = ref(false);

// 🎯 开发模式：支持URL参数和localStorage记忆当前步骤
const isDevelopmentMode = ref(process.env.NODE_ENV === 'development'); // 基于环境变量自动判断

// ========================================
// 🧮 计算属性
// ========================================
// 总步骤数
const totalSteps = computed(() => guideSteps.value.length);

// 当前引导步骤对象
const currentGuideStep = computed(() => {
	return guideSteps.value[currentStep.value - 1] || guideSteps.value[0];
});



// ========================================
// 🔧 核心方法
// ========================================
/**
 * 检查是否为首次访问
 * 
 * 🎯 功能：检查localStorage中是否有引导完成标记
 * 📊 返回值：true表示首次访问，false表示已完成引导
 */
const checkFirstVisit = () => {
	try {
		const completed = localStorage.getItem('digital-human-guide-completed');
		return completed !== 'true';
	} catch (error) {
		console.error('检查引导状态失败:', error);
		return true; // 默认显示引导
	}
};

/**
 * 显示引导
 * 
 * 🎯 功能：显示用户引导界面
 */
const showGuide = () => {
	isVisible.value = true;

	// 🎯 开发模式下不重置步骤，保持当前步骤
	if (!isDevelopmentMode.value) {
		currentStep.value = 1;
	}

	// 聚焦到引导容器，支持键盘导航
	nextTick(() => {
		const guideElement = document.querySelector('.user-guide-overlay');
		if (guideElement) {
			guideElement.focus();
		}
	});
};

/**
 * 下一步
 * 
 * 🎯 功能：切换到下一张引导图片或完成引导
 */
const nextStep = async () => {
	if (isTransitioning.value) return;

	isTransitioning.value = true;

	try {
		if (currentStep.value < totalSteps.value) {
			// 切换到下一步
			currentStep.value++;
			// 🎯 开发模式：保存当前步骤
			saveDevelopmentStep();
		} else {
			// 完成引导
			await completeGuide();
		}
	} catch (error) {
		console.error('切换引导步骤失败:', error);
	} finally {
		// 延迟重置过渡状态，防止快速点击
		setTimeout(() => {
			isTransitioning.value = false;
		}, 300);
	}
};

/**
 * 上一步
 * 
 * 🎯 功能：返回到上一张引导图片
 */
const prevStep = () => {
	if (isTransitioning.value || currentStep.value <= 1) return;

	isTransitioning.value = true;
	currentStep.value--;
	// 🎯 开发模式：保存当前步骤
	saveDevelopmentStep();

	setTimeout(() => {
		isTransitioning.value = false;
	}, 300);
};

/**
 * 🎯 处理多个点击区域的点击事件
 *
 * @param {string} action - 点击区域的动作类型 ('prev' | 'next' | 'close')
 */
const handleAreaClick = (action) => {
	if (action === 'prev') {
		prevStep();
	} else if (action === 'next') {
		nextStep();
	} else if (action === 'close') {
		closeGuide();
	}
};

// ========================================
// 🛠️ 开发模式相关功能
// ========================================

/**
 * 🎯 初始化开发模式
 * 支持URL参数 ?step=3 和 localStorage记忆
 * 首次访问时总是从第1步开始
 */
const initDevelopmentMode = () => {
	try {
		// 0. 首次访问检测 - 优先级最高
		if (checkFirstVisit()) {
			console.log('🎯 开发模式：首次访问，从第1步开始');
			currentStep.value = 1;
			return;
		}

		// 1. 检查URL参数
		const urlParams = new URLSearchParams(window.location.search);
		const stepParam = urlParams.get('step');

		if (stepParam) {
			const step = parseInt(stepParam);
			if (step >= 1 && step <= totalSteps.value) {
				currentStep.value = step;
				console.log(`🎯 开发模式：从URL参数跳转到第${step}步`);
				return;
			}
		}

		// 2. 检查localStorage记忆的步骤（仅非首次访问）
		const savedStep = localStorage.getItem('userGuide_currentStep');
		if (savedStep) {
			const step = parseInt(savedStep);
			if (step >= 1 && step <= totalSteps.value) {
				currentStep.value = step;
				console.log(`🎯 开发模式：从localStorage恢复到第${step}步`);
				return;
			}
		}

		console.log('🎯 开发模式：使用默认第1步');
	} catch (error) {
		console.error('🎯 开发模式初始化失败:', error);
	}
};

/**
 * 🎯 保存当前步骤到localStorage（开发模式）
 */
const saveDevelopmentStep = () => {
	if (isDevelopmentMode.value) {
		try {
			localStorage.setItem('userGuide_currentStep', currentStep.value.toString());
			console.log(`🎯 开发模式：保存当前步骤 ${currentStep.value}`);
		} catch (error) {
			console.error('🎯 开发模式保存步骤失败:', error);
		}
	}
};

/**
 * 跳过引导
 * 
 * 🎯 功能：跳过引导并标记为已完成
 */
const skipGuide = async () => {
	if (isTransitioning.value) return;
	await completeGuide();
};

/**
 * 完成引导
 *
 * 🎯 功能：隐藏引导界面并记录完成状态
 */
const completeGuide = async () => {
	try {
		// 记录引导已完成
		localStorage.setItem('digital-human-guide-completed', 'true');

		// 隐藏引导界面
		isVisible.value = false;

		// 触发完成事件
		emit('guide-completed');

		console.log('用户引导已完成');
	} catch (error) {
		console.error('完成引导失败:', error);
	}
};

/**
 * 关闭引导
 *
 * 🎯 功能：用户点击X号按钮关闭引导，同样标记为已完成
 */
const closeGuide = async () => {
	try {
		// 记录引导已完成（用户主动关闭也视为完成）
		localStorage.setItem('digital-human-guide-completed', 'true');

		// 隐藏引导界面
		isVisible.value = false;

		// 触发完成事件
		emit('guide-completed');

		console.log('用户引导已关闭');
	} catch (error) {
		console.error('关闭引导失败:', error);
	}
};

/**
 * 重置引导状态（开发用）
 * 
 * 🎯 功能：清除引导完成标记，用于开发测试
 */
const resetGuide = () => {
	try {
		localStorage.removeItem('digital-human-guide-completed');
		currentStep.value = 1;
		console.log('引导状态已重置');
	} catch (error) {
		console.error('重置引导状态失败:', error);
	}
};

// ========================================
// 🎮 事件处理方法
// ========================================
/**
 * 处理键盘事件
 * 
 * 🎯 功能：支持键盘导航
 * - 空格键/回车键：下一步
 * - ESC键：跳过引导
 * - 左箭头：上一步
 * - 右箭头：下一步
 */
const handleKeydown = (event) => {
	if (isTransitioning.value) return;

	switch (event.key) {
		case ' ':
		case 'Enter':
			event.preventDefault();
			nextStep();
			break;
		case 'Escape':
			event.preventDefault();
			skipGuide();
			break;
		case 'ArrowLeft':
			event.preventDefault();
			prevStep();
			break;
		case 'ArrowRight':
			event.preventDefault();
			nextStep();
			break;
	}
};

/**
 * 处理背景点击
 * 
 * 🎯 功能：点击背景区域不执行任何操作（防止误操作）
 */
const handleBackdropClick = (event) => {
	// 阻止事件冒泡，防止意外关闭引导
	event.stopPropagation();
};

/**
 * 处理图片加载完成
 * 
 * 🎯 功能：图片加载完成后的处理
 */
const handleImageLoad = () => {
	isLoading.value = false;
};

/**
 * 处理图片加载错误
 * 
 * 🎯 功能：图片加载失败时的处理
 */
const handleImageError = (event) => {
	console.error('引导图片加载失败:', event.target.src);
	isLoading.value = false;
	// 可以在这里添加默认图片或错误提示
};

// ========================================
// 🔗 组件通信
// ========================================
// 定义组件事件
const emit = defineEmits(['guide-completed', 'guide-skipped']);

// 暴露给父组件的方法
defineExpose({
	showGuide,
	resetGuide,
	checkFirstVisit
});

// ========================================
// 🔄 生命周期钩子
// ========================================
onMounted(() => {
	// 🎯 统一的首次访问检测 - 无论开发模式还是生产模式
	if (checkFirstVisit()) {
		console.log('首次访问，准备显示引导');

		// 开发模式：支持URL参数和localStorage记忆步骤
		if (isDevelopmentMode.value) {
			initDevelopmentMode();
		}

		// 延迟显示，确保页面加载完成
		setTimeout(() => {
			showGuide();
		}, 500);
	} else {
		console.log('用户已完成引导，跳过显示');
	}
});

onUnmounted(() => {
	// 清理工作（如果需要）
});
</script>

<style scoped lang="scss">
/* ========================================
   🌐 用户引导覆盖层样式
   ======================================== */

/**
 * 引导覆盖层主容器
 *
 * 🎯 功能：全屏覆盖，确保引导内容在最顶层显示
 * 📍 定位：固定定位覆盖整个视口
 * 🎨 视觉：半透明背景，营造专注的引导环境
 */
.user-guide-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10000; // 确保在所有内容之上
	display: flex;
	align-items: center;
	justify-content: center;
	outline: none; // 移除焦点轮廓

	// 淡入动画
	animation: fadeIn 0.3s ease-out;
}

/**
 * 背景遮罩层
 *
 * 🎯 功能：提供半透明背景，突出引导内容
 */
.guide-backdrop {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(2px); // 背景模糊效果
}

/**
 * 引导内容容器
 *
 * 🎯 功能：承载引导图片和控制元素
 * 📐 布局：全屏布局，图片占满屏幕
 */
.guide-content-container {
	position: relative;
	z-index: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100vw;
	height: 100vh;
}

/* ========================================
   🖼️ 引导图片样式
   ======================================== */

/**
 * 图片包装器
 *
 * 🎯 功能：为图片提供相对定位基准，用于透明点击区域定位
 */
.guide-image-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100vw;
	height: 100vh;
	margin-bottom: 0; // 移除底部边距
}

/**
 * 引导图片
 *
 * 🎯 功能：显示引导内容图片
 * 📐 尺寸：占满全屏显示
 * 🎨 效果：无边框，全屏展示
 */
.guide-image {
	width: 100vw;
	height: 100vh;
	object-fit: cover; // 保持比例填满屏幕
	border-radius: 0; // 移除圆角
	box-shadow: none; // 移除阴影

	// 图片切换过渡效果
	transition: opacity 0.3s ease-in-out;
}

/**
 * 透明点击区域
 *
 * 🎯 功能：精确定位到图片中"下一步"按钮的位置
 * 🎮 交互：鼠标悬停时显示提示效果
 */
.click-area {
	// 基础样式在JavaScript中动态设置
	border-radius: 4px;
	transition: all 0.2s ease;



	// 激活效果
	&:active {
		background-color: rgba(64, 158, 255, 0.3);
		transform: scale(0.98);
	}
}



/* ========================================
   🎮 控制按钮样式
   ======================================== */

/**
 * 控制按钮容器
 *
 * 🎯 功能：承载引导控制按钮
 */
.guide-controls {
	position: absolute;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 12px;
	align-items: center;
	z-index: 10;

	// 响应式调整
	@media (max-width: 480px) {
		flex-direction: column;
		gap: 8px;
	}
}

/**
 * 按钮基础样式
 *
 * 🎯 功能：统一的按钮外观和交互效果
 */
%button-base {
	padding: 10px 20px;
	border: none;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	outline: none;

	// 禁用状态
	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}



	// 激活效果
	&:not(:disabled):active {
		transform: translateY(0);
	}

	// 响应式调整
	@media (max-width: 480px) {
		padding: 8px 16px;
		font-size: 13px;
		min-width: 80px;
	}
}

/**
 * 跳过按钮
 *
 * 🎯 功能：允许用户跳过引导
 */
.skip-button {
	@extend %button-base;
	background-color: rgba(255, 255, 255, 0.1);
	color: #ffffff;
	border: 1px solid rgba(255, 255, 255, 0.3);


}

/**
 * 上一步按钮
 *
 * 🎯 功能：返回到上一个引导步骤
 */
.prev-button {
	@extend %button-base;
	background-color: #909399;
	color: #ffffff;


}

/**
 * 下一步/完成按钮
 *
 * 🎯 功能：进入下一步或完成引导
 */
.next-button {
	@extend %button-base;
	background: linear-gradient(135deg, #409EFF, #67C23A);
	color: #ffffff;


}

/* ========================================
   ⏳ 加载状态样式
   ======================================== */

/**
 * 加载覆盖层
 *
 * 🎯 功能：显示加载状态
 */
.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.7);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 2;
	border-radius: 8px;
}

/**
 * 加载动画
 *
 * 🎯 功能：旋转加载指示器
 */
.loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid rgba(255, 255, 255, 0.3);
	border-top: 3px solid #409EFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 16px;
}

/**
 * 加载文字
 *
 * 🎯 功能：显示加载提示文字
 */
.loading-text {
	color: #ffffff;
	font-size: 14px;
	margin: 0;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ========================================
   🎬 动画定义
   ======================================== */

/**
 * 淡入动画
 *
 * 🎯 功能：引导界面显示时的淡入效果
 */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: scale(0.95);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

/**
 * 旋转动画
 *
 * 🎯 功能：加载指示器的旋转效果
 */
@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* ========================================
   📱 响应式设计
   ======================================== */

/**
 * 平板设备适配
 */
@media (max-width: 1024px) {
	.progress-indicator {
		min-width: 180px;
	}

	.progress-bar {
		width: 180px;
	}
}

/**
 * 手机设备适配
 */
@media (max-width: 768px) {
	.progress-indicator {
		min-width: 160px;
	}

	.progress-bar {
		width: 160px;
		height: 3px;
	}

	.progress-text {
		font-size: 13px;
	}
}

/**
 * 小屏手机适配
 */
@media (max-width: 480px) {
	.progress-indicator {
		min-width: 140px;
	}

	.progress-bar {
		width: 140px;
	}
}
</style>
